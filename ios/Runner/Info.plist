<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>Equal Cash</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>Equal Cash</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    <key>NSFaceIDUsageDescription</key>
    <string>Equal cash needs Face ID for secure identity verification.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>Equal cash needs access to your photo library to upload images.</string>
    <key>NSCameraUsageDescription</key>
    <string>Equal cash needs access to your camera to upload images.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>Equal cash needs access to your microphone to record audio.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>Equal cash needs location access for identity verification purposes.</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>Equal cash needs location access for identity verification purposes.</string>
    <key>NSDocumentsFolderUsageDescription</key>
    <string>Equal cash needs access to documents for identity verification.</string>
    <key>NSFileProviderDomainUsageDescription</key>
    <string>Equal cash needs file access for document verification.</string>
    <key>NSAppleMusicUsageDescription</key>
    <string>Equal cash needs media access for verification purposes.</string>
    <key>NSMotionUsageDescription</key>
    <string>Equal cash needs motion data for enhanced security verification.</string>
  </dict>
</plist>