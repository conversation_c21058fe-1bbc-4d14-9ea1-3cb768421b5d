import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/screens/splash/splash_screen.dart';
import 'package:smile_id/smile_id.dart';
import 'package:smile_id/smileid_messages.g.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppInitService().init();

  // Load SmileID configuration from JSON file
  try {
    final smileConfig = await SmileConfigModel.loadFromAssets();
    SmileID.initializeWithApiKey(
      apiKey: "************************************",
      useSandbox: true,
      config: FlutterConfig(
        partnerId: smileConfig.partnerId,
        authToken: smileConfig.authToken,
        prodBaseUrl: smileConfig.prodLambdaUrl,
        sandboxBaseUrl: smileConfig.testLambdaUrl,
      ),
      // config: FlutterConfig(
      //   partnerId: smileConfig.partnerId,
      //   authToken: smileConfig.authToken,
      //   prodBaseUrl: smileConfig.prodLambdaUrl,
      //   sandboxBaseUrl: smileConfig.testLambdaUrl,
      // ),
      enableCrashReporting: false,
    );
    printty('SmileID initialized successfully');
  } catch (e) {
    printty('Failed to initialize SmileID: $e');
    // You might want to handle this error appropriately for your app
  }

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: (context, child) {
        return MaterialApp(
          title: 'Equal Cash',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            fontFamily: 'Nohemi',
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
            useMaterial3: true,
          ),
          navigatorKey: NavKey.appNavigatorKey,
          // home: const DashboardNavigationScreen(),
          home: const SplashScreen(),
          onGenerateRoute: AppRouter.getRoute,
        );
      },
    );
  }
}
