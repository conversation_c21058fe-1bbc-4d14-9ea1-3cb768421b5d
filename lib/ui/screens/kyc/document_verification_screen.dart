import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:equalcash/ui/screens/kyc/verification_success_screen.dart';
import 'package:smile_id/products/enhanceddocv/smile_id_enhanced_document_verification.dart';

class DocumentVerificationScreen extends ConsumerStatefulWidget {
  const DocumentVerificationScreen({
    super.key,
    required this.countryCode,
    required this.documentType,
    required this.countryName,
    required this.documentName,
    required this.userReference,
    required this.jobId,
  });

  final String countryCode;
  final String? documentType;
  final String countryName;
  final String documentName;
  final String userReference;
  final String jobId;

  @override
  ConsumerState<DocumentVerificationScreen> createState() =>
      _DocumentVerificationScreenState();
}

class _DocumentVerificationScreenState
    extends ConsumerState<DocumentVerificationScreen> {
  bool isLoading = false;
  bool hasStartedVerification = false;
  bool isGeneratingToken = false;

  @override
  Widget build(BuildContext context) {
    printty("userrefrense  ${widget.userReference}");
    printty("jobid  ${widget.jobId}");
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(
        headerText: 'Document Verification',
      ),
      body: hasStartedVerification
          ? _buildVerificationWidget()
          : _buildPreVerificationScreen(),
    );
  }

  Widget _buildPreVerificationScreen() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
      child: Column(
        children: [
          const YBox(40),

          // Document Details
          Container(
            padding: EdgeInsets.all(Sizer.radius(20)),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(Sizer.radius(16)),
              border: Border.all(color: AppColors.secondary200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Verification Details',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.neutral400,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const YBox(16),
                _buildDetailRow('Country', widget.countryName),
                const YBox(12),
                _buildDetailRow('Document Type', widget.documentName),
                const YBox(12),
                _buildDetailRow(
                    'Verification Method', 'Enhanced Document Verification'),
              ],
            ),
          ),

          const YBox(40),

          // Instructions
          Container(
            padding: EdgeInsets.all(Sizer.radius(20)),
            decoration: BoxDecoration(
              color: AppColors.accent50,
              borderRadius: BorderRadius.circular(Sizer.radius(16)),
              border: Border.all(color: AppColors.secondary200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Iconsax.info_circle,
                      size: Sizer.radius(20),
                      color: AppColors.primaryPurple,
                    ),
                    const XBox(8),
                    Text(
                      'Before you start',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.primaryPurple,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const YBox(12),
                _buildInstructionItem('Ensure good lighting'),
                _buildInstructionItem('Hold your document steady'),
                _buildInstructionItem('Make sure all text is clearly visible'),
                _buildInstructionItem('Avoid glare and shadows'),
              ],
            ),
          ),

          const YBox(40),

          // Start Verification Button
          CustomBtn.solid(
            text: isLoading
                ? (isGeneratingToken
                    ? 'Initializing...'
                    : 'Starting Verification...')
                : 'Start Verification',
            isLoading: isLoading,
            onTap: isLoading ? null : _startVerification,
          ),

          const YBox(100),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: Sizer.width(100),
          child: Text(
            label,
            style: AppTypography.text12.copyWith(
              color: AppColors.neutral300,
            ),
          ),
        ),
        const XBox(16),
        Expanded(
          child: Text(
            value,
            style: AppTypography.text12.copyWith(
              color: AppColors.neutral400,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInstructionItem(String instruction) {
    return Padding(
      padding: EdgeInsets.only(bottom: Sizer.height(8)),
      child: Row(
        children: [
          Container(
            width: Sizer.radius(6),
            height: Sizer.radius(6),
            decoration: const BoxDecoration(
              color: AppColors.primaryPurple,
              shape: BoxShape.circle,
            ),
          ),
          const XBox(12),
          Expanded(
            child: Text(
              instruction,
              style: AppTypography.text12.copyWith(
                color: AppColors.neutral400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationWidget() {
    printty("Building verification widget with:");
    printty("Country Code: ${widget.countryCode}");
    printty("Document Type: ${widget.documentType}");
    printty("User Reference: ${widget.userReference}");
    printty("Job ID: ${widget.jobId}");

    return SmileIDEnhancedDocumentVerification(
      countryCode: widget.countryCode,
      documentType: widget.documentType,
      useStrictMode: false, // Try with strict mode disabled for iOS
      userId: widget.userReference,
      jobId: widget.jobId,
      consentGrantedDate: DateTime.now().toIso8601String(),
      personalDetailsConsentGranted: true,
      contactInformationConsentGranted: true,
      documentInformationConsentGranted: true,
      // Add additional parameters for better iOS compatibility
      allowAgentMode: false,
      allowGalleryUpload: true,
      allowNewEnroll: false,
      showAttribution: true,
      showInstructions: true,
      onSuccess: _onVerificationSuccess,
      onError: _onVerificationError,
    );
  }

  void _startVerification() async {
    setState(() {
      isLoading = true;
      isGeneratingToken = true;
    });

    try {
      // Generate SDK token before starting verification
      final kycVm = ref.read(kycVmodel);
      final tokenResponse = await kycVm.generateSDKToken();

      if (tokenResponse.success) {
        printty("SDK Token generated successfully: ${kycVm.sdkToken?.jobId}");

        // Add a small delay to show loading state
        await Future.delayed(const Duration(milliseconds: 1000));

        if (mounted) {
          setState(() {
            isLoading = false;
            isGeneratingToken = false;
            hasStartedVerification = true;
          });
        }
      } else {
        printty("Failed to generate SDK token: ${tokenResponse.message}");
        if (mounted) {
          setState(() {
            isLoading = false;
            isGeneratingToken = false;
          });

          // Show error dialog
          _showErrorDialog(
              "Failed to initialize verification. Please try again.");
        }
      }
    } catch (e) {
      printty("Error generating SDK token: $e");
      if (mounted) {
        setState(() {
          isLoading = false;
          isGeneratingToken = false;
        });

        // Show error dialog
        _showErrorDialog(
            "Failed to initialize verification. Please try again.");
      }
    }
  }

  void _onVerificationSuccess(String? result) {
    printty("Verification Success: $result");

    final ctx = NavKey.appNavigatorKey.currentContext!;
    Navigator.of(ctx).pop();
    // Navigate to success screen or handle success
    Navigator.of(ctx).pushReplacement(
      MaterialPageRoute(
        builder: (ctx) => VerificationSuccessScreen(
          result: result,
          countryName: widget.countryName,
          documentName: widget.documentName,
        ),
      ),
    );
  }

  void _onVerificationError(String errorMessage) {
    printty("Verification Error: $errorMessage");

    // Check if this is the specific authorization error
    if (errorMessage.contains("2205") ||
        errorMessage.toLowerCase().contains("not authorized")) {
      printty("iOS Authorization Error Detected - Error 2205");
      printty("This might be related to iOS-specific configuration issues");
    }

    final ctx = NavKey.appNavigatorKey.currentContext!;
    Navigator.of(ctx).pop();

    // Provide more specific error message for authorization errors
    String displayMessage = errorMessage;
    if (errorMessage.contains("2205")) {
      displayMessage =
          "Authorization failed. This might be due to iOS-specific configuration. Please contact support if the issue persists.\n\nError: $errorMessage";
    }

    // Show error dialog and allow retry
    showDialog(
      context: ctx,
      builder: (ctx) => AlertDialog(
        title: const Text('Verification Failed'),
        content: Text(displayMessage),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              setState(() {
                hasStartedVerification = false;
              });
            },
            child: const Text('Try Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              Navigator.of(ctx).pop(); // Go back to document selection
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    final ctx = NavKey.appNavigatorKey.currentContext!;
    showDialog(
      context: ctx,
      builder: (ctx) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
