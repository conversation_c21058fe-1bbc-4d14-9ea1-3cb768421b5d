import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class VerifyYourIdentityScreen extends StatelessWidget {
  const VerifyYourIdentityScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Verify Your Identity',
            subtitle: "Please enter the information below",
          ),
          const YBox(48),
          Text(
            'Verifying your identity helps keep everyone’s money safe and ensure that you are really you. This information is never share with anyone else. You can now verify your identity instantly and securely using your approved identification document and your picture.',
            style: AppTypography.text13.copyWith(
              color: AppColors.neutral300,
              height: 1.6,
            ),
          ),
          const YBox(48),
          Text(
            'Use your device to :',
            style: AppTypography.text16.copyWith(
              color: AppColors.neutral300,
              fontWeight: FontWeight.w400,
            ),
          ),
          const YBox(16),
          const IdentityListTile(
            number: '1',
            title: 'Scan your identity document',
          ),
          const YBox(16),
          const IdentityListTile(
            number: '2',
            title: 'Take a picture of your face',
          ),
          const YBox(90),
          CustomBtn.solid(
            text: 'Continue',
            onTap: () {
              Navigator.pushNamed(context, RoutePath.chooseYourDocumentScreen);
            },
          ),
        ],
      ),
    );
  }
}

class IdentityListTile extends StatelessWidget {
  const IdentityListTile({
    super.key,
    required this.number,
    required this.title,
  });

  final String number;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: Sizer.height(24),
          width: Sizer.width(24),
          decoration: BoxDecoration(
            color: AppColors.primaryPurple,
            borderRadius: BorderRadius.circular(
              Sizer.radius(20),
            ),
          ),
          child: Center(
            child: Text(
              number,
              style: AppTypography.text15.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const XBox(16),
        Text(
          title,
          style: AppTypography.text14.copyWith(
            color: AppColors.neutral300,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
