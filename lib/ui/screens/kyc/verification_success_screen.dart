import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class VerificationSuccessScreen extends ConsumerStatefulWidget {
  const VerificationSuccessScreen({
    super.key,
    required this.result,
    required this.countryName,
    required this.documentName,
  });

  final String? result;
  final String countryName;
  final String documentName;

  @override
  ConsumerState<VerificationSuccessScreen> createState() =>
      _VerificationSuccessScreenState();
}

class _VerificationSuccessScreenState
    extends ConsumerState<VerificationSuccessScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(
        headerText: 'Verification Complete',
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              children: [
                const YBox(40),
                
                // Success Icon
                Center(
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      width: Sizer.radius(120),
                      height: Sizer.radius(120),
                      decoration: BoxDecoration(
                        color: AppColors.green00,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.green00.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        Iconsax.tick_circle5,
                        size: Sizer.radius(60),
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ),
                
                const YBox(32),
                
                // Success Message
                Text(
                  'Verification Successful!',
                  textAlign: TextAlign.center,
                  style: AppTypography.text24.copyWith(
                    color: AppColors.neutral400,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                
                const YBox(12),
                
                Text(
                  'Your ${widget.documentName.toLowerCase()} has been successfully verified.',
                  textAlign: TextAlign.center,
                  style: AppTypography.text16.copyWith(
                    color: AppColors.neutral300,
                    height: 1.5,
                  ),
                ),
                
                const YBox(40),
                
                // Verification Details Card
                Container(
                  padding: EdgeInsets.all(Sizer.radius(24)),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(Sizer.radius(16)),
                    border: Border.all(color: AppColors.secondary200),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.neutral100.withOpacity(0.5),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            decoration: BoxDecoration(
                              color: AppColors.green00.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(Sizer.radius(12)),
                            ),
                            child: Icon(
                              Iconsax.shield_tick,
                              size: Sizer.radius(24),
                              color: AppColors.green00,
                            ),
                          ),
                          const XBox(16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Identity Verified',
                                  style: AppTypography.text16.copyWith(
                                    color: AppColors.neutral400,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const YBox(4),
                                Text(
                                  'Your document has passed all security checks',
                                  style: AppTypography.text13.copyWith(
                                    color: AppColors.neutral300,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const YBox(24),
                      
                      // Verification Details
                      _buildDetailRow('Country', widget.countryName),
                      const YBox(16),
                      _buildDetailRow('Document Type', widget.documentName),
                      const YBox(16),
                      _buildDetailRow('Verification Date', _formatDate(DateTime.now())),
                      const YBox(16),
                      _buildDetailRow('Status', 'Verified', isStatus: true),
                    ],
                  ),
                ),
                
                const YBox(32),
                
                // Next Steps Card
                Container(
                  padding: EdgeInsets.all(Sizer.radius(20)),
                  decoration: BoxDecoration(
                    color: AppColors.accent50,
                    borderRadius: BorderRadius.circular(Sizer.radius(16)),
                    border: Border.all(color: AppColors.secondary200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Iconsax.info_circle,
                            size: Sizer.radius(20),
                            color: AppColors.primaryPurple,
                          ),
                          const XBox(8),
                          Text(
                            'What\'s Next?',
                            style: AppTypography.text16.copyWith(
                              color: AppColors.primaryPurple,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const YBox(12),
                      
                      Text(
                        'Your identity verification is complete. You can now access all features of the app and proceed with your transactions.',
                        style: AppTypography.text14.copyWith(
                          color: AppColors.neutral400,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const YBox(40),
                
                // Continue Button
                CustomBtn.solid(
                  text: 'Continue to Dashboard',
                  onTap: _navigateToDashboard,
                ),
                
                const YBox(16),
                
                // View Details Button
                CustomBtn.solid(
                  text: 'View Verification Details',
                  isOutline: true,
                  onTap: _showVerificationDetails,
                ),
                
                const YBox(100),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isStatus = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: Sizer.width(100),
          child: Text(
            label,
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral300,
            ),
          ),
        ),
        const XBox(16),
        Expanded(
          child: Row(
            children: [
              if (isStatus) ...[
                Container(
                  width: Sizer.radius(8),
                  height: Sizer.radius(8),
                  decoration: BoxDecoration(
                    color: AppColors.green00,
                    shape: BoxShape.circle,
                  ),
                ),
                const XBox(8),
              ],
              Expanded(
                child: Text(
                  value,
                  style: AppTypography.text14.copyWith(
                    color: isStatus ? AppColors.green00 : AppColors.neutral400,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _navigateToDashboard() {
    // Navigate to dashboard or main screen
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/dashboard',
      (route) => false,
    );
  }

  void _showVerificationDetails() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(Sizer.radius(20)),
            topRight: Radius.circular(Sizer.radius(20)),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: Sizer.width(40),
              height: Sizer.height(4),
              margin: EdgeInsets.only(top: Sizer.height(12)),
              decoration: BoxDecoration(
                color: AppColors.neutral200,
                borderRadius: BorderRadius.circular(Sizer.radius(2)),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(Sizer.radius(24)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Verification Details',
                    style: AppTypography.text20.copyWith(
                      color: AppColors.neutral400,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const YBox(20),
                  
                  if (widget.result != null) ...[
                    Text(
                      'Raw Result:',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral400,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const YBox(8),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(Sizer.radius(16)),
                      decoration: BoxDecoration(
                        color: AppColors.neutral100,
                        borderRadius: BorderRadius.circular(Sizer.radius(8)),
                      ),
                      child: Text(
                        widget.result!,
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral400,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ] else ...[
                    Text(
                      'No additional details available.',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral300,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}