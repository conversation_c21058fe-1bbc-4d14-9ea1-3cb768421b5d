import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:equalcash/ui/screens/kyc/document_verification_screen.dart';

class ChooseYourDocumentScreen extends ConsumerStatefulWidget {
  const ChooseYourDocumentScreen({super.key});

  @override
  ConsumerState<ChooseYourDocumentScreen> createState() =>
      _ChooseYourDocumentScreenState();
}

class _ChooseYourDocumentScreenState
    extends ConsumerState<ChooseYourDocumentScreen> {
  final countryC = TextEditingController();
  final countryF = FocusNode();

  VerificationCountriesModel? selectedCountry;
  String? selectedDocumentType;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(kycVmodel).generateSDKToken();
    });
  }

  @override
  void dispose() {
    countryC.dispose();
    countryF.dispose();
    super.dispose();
  }

  String _getDocumentName(String idType) {
    switch (idType.toLowerCase()) {
      case 'passport':
        return 'Passport';
      case 'drivers_license':
      case 'driver_license':
      case 'driving_license':
        return 'Driver\'s License';
      case 'national_id':
      case 'national_identity_card':
        return 'National Identity Card';
      default:
        return 'Document';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Choose Your Document',
            subtitle: "Select issuing country to see which documents we accept",
          ),
          const YBox(30),
          CustomTextField(
            controller: countryC,
            focusNode: countryF,
            isReadOnly: true,
            labelText: 'Country',
            hintText: 'Select your country',
            showLabelHeader: true,
            suffixIcon: Icon(
              Iconsax.arrow_down_1,
              size: Sizer.radius(24),
            ),
            onTap: () async {
              final result = await ModalWrapper.bottomSheet(
                context: context,
                widget: CountryModal(selectedCountry: selectedCountry?.name),
              );

              if (result != null && result is Map<String, dynamic>) {
                final countryName = result['name'] as String?;
                final countryCode = result['code'] as String?;

                if (countryName != null) {
                  // Find the full country object from the KYC view model
                  final countries = ref.read(kycVmodel).verificationCountries;
                  final country = countries.firstWhere(
                    (c) => c.name == countryName && c.code == countryCode,
                    orElse: () => VerificationCountriesModel(
                      name: countryName,
                      code: countryCode,
                      supportedDocuments: [],
                    ),
                  );

                  setState(() {
                    selectedCountry = country;
                    countryC.text = countryName;
                    selectedDocumentType = null; // Reset selected document
                  });
                }
              }
            },
          ),

          // Supported Documents Section
          if (selectedCountry != null &&
              selectedCountry!.supportedDocuments != null &&
              selectedCountry!.supportedDocuments!.isNotEmpty) ...[
            const YBox(32),
            Text(
              'ACCEPTED DOCUMENTS',
              style: AppTypography.text12.copyWith(
                color: AppColors.neutral300,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
            const YBox(16),
            ...selectedCountry!.supportedDocuments!.map((document) {
              final isSelected = selectedDocumentType == document.idType;
              return Container(
                margin: EdgeInsets.only(bottom: Sizer.height(12)),
                child: DocumentTile(
                  document: document,
                  isSelected: isSelected,
                  onTap: () {
                    setState(() {
                      selectedDocumentType =
                          isSelected ? null : document.idType;
                    });
                  },
                ),
              );
            }),
          ],

          const YBox(100),
        ],
      ),
      bottomSheet: selectedDocumentType == null
          ? const SizedBox.shrink()
          : Padding(
              padding: EdgeInsets.only(
                left: Sizer.width(24),
                right: Sizer.width(24),
                bottom: Sizer.height(24),
                top: Sizer.height(5),
              ),
              child: CustomBtn.solid(
                text: 'Continue',
                onTap: () {
                  final kymVm = ref.read(kycVmodel);
                  printty(
                      "Country code ${selectedCountry?.code} Document type $selectedDocumentType");
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => DocumentVerificationScreen(
                        jobId: kymVm.sdkToken?.jobId ?? "",
                        userReference:
                            kymVm.sdkToken?.userReference?.toString() ?? "",
                        countryCode: selectedCountry?.code ?? '',
                        documentType: selectedDocumentType,
                        countryName: selectedCountry?.name ?? '',
                        documentName:
                            _getDocumentName(selectedDocumentType ?? ''),
                      ),
                    ),
                  );
                },
              ),
            ),
    );
  }
}

class DocumentTile extends StatelessWidget {
  const DocumentTile({
    super.key,
    required this.document,
    this.isSelected = false,
    this.onTap,
  });

  final SupportedDocument document;
  final bool isSelected;
  final VoidCallback? onTap;

  IconData _getDocumentIcon(String? idType) {
    switch (idType?.toLowerCase()) {
      case 'passport':
        return Iconsax.book;
      case 'driver_license':
      case 'driving_license':
        return Iconsax.car;
      case 'national_id':
      case 'national_identity_card':
        return Iconsax.card;
      default:
        return Iconsax.document;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.all(Sizer.radius(16)),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Sizer.radius(12)),
          border: Border.all(
            color: isSelected ? AppColors.primaryPurple : AppColors.neutral200,
            width: isSelected ? 2 : 1,
          ),
          color: isSelected ? AppColors.secondary50 : AppColors.white,
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(Sizer.radius(8)),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primaryPurple.withOpacity(0.1)
                    : AppColors.white,
                borderRadius: BorderRadius.circular(Sizer.radius(8)),
              ),
              child: Icon(
                _getDocumentIcon(document.idType),
                size: Sizer.radius(20),
                color:
                    isSelected ? AppColors.primaryPurple : AppColors.neutral400,
              ),
            ),
            const XBox(16),
            Expanded(
              child: Text(
                document.idName ?? 'Document',
                style: AppTypography.text14.copyWith(
                  color: AppColors.neutral400,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (isSelected)
              Container(
                padding: EdgeInsets.all(Sizer.radius(4)),
                margin: EdgeInsets.only(
                  left: Sizer.width(8),
                ),
                decoration: const BoxDecoration(
                  color: AppColors.primaryPurple,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  size: Sizer.radius(10),
                  color: AppColors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
