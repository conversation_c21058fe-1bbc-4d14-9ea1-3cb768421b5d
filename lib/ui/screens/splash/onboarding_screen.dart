import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
// import 'package:smile_id/products/enhanceddocv/smile_id_enhanced_document_verification.dart';  // Temporarily commented out

class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        bottom: false,
        child: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              children: [
                const YBox(70),
                imageHelper(
                  AppImages.onb1,
                  height: Sizer.height(280),
                ),
                const YBox(30),
                Text(
                  "Get Cash at Zero Interest \nRate, for Down Payment \nand as Savings.",
                  textAlign: TextAlign.center,
                  style: AppTypography.text24.copyWith(
                    color: AppColors.neutral400,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                CustomBtn.solid(
                  onTap: () {
                    NavigationHelper.navigateTo(
                      routeName: RoutePath.createAccountScreen,
                    );
                  },
                  text: "Get Started",
                ),
                const YBox(12),
                CustomBtn.solid(
                  onlineColor: AppColors.purpleB0,
                  onTap: () {
                    NavigationHelper.navigateTo(
                      routeName: RoutePath.passwordLoginScreen,
                    );
                  },
                  text: "Login",
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
