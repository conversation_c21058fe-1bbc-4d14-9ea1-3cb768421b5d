import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/ui.dart';

class DashboardNavigationScreen extends ConsumerStatefulWidget {
  const DashboardNavigationScreen({
    super.key,
    this.index,
  });

  final int? index;

  @override
  ConsumerState<DashboardNavigationScreen> createState() =>
      _DashboardNavigationScreenState();
}

class _DashboardNavigationScreenState
    extends ConsumerState<DashboardNavigationScreen> {
  int currentIndex = 0;
  List screens = [
    const HomeScreen(),
    const ContributionScreen(),
    const NotificationScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileVm).getUser();
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: screens[currentIndex],
        floatingActionButton: InkWell(
          onTap: () {
            // ModalWrapper.bottomSheet(
            //   context: context,
            //   widget: InfoActionModal(
            //     title: 'Complete KYC',
            //     content:
            //         'To proceed and create a contribution company, \nplease complete the KYC',
            //     btnText: 'Complete KYC',
            //     onAction: () {},
            //   ),
            // );
            NavigationHelper.navigateTo(
              routeName: RoutePath.startCompanyScreen,
            );
          },
          child: Container(
            height: Sizer.height(60),
            width: Sizer.width(60),
            decoration: const BoxDecoration(
              color: AppColors.primaryPurple,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.add,
                color: AppColors.white,
                size: Sizer.text(30),
              ),
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        bottomNavigationBar: BottomAppBar(
          shape: const CircularNotchedRectangle(),
          notchMargin: Sizer.height(8),
          color: AppColors.primaryPurple,
          shadowColor: Colors.transparent,
          elevation: 0,
          clipBehavior: Clip.hardEdge,
          padding: EdgeInsets.zero,
          surfaceTintColor: Colors.transparent,
          child: Container(
            height: Sizer.height(84),
            padding: EdgeInsets.only(
              bottom: Sizer.height(5),
            ),
            decoration: const BoxDecoration(
              color: AppColors.transparent,
              // boxShadow: [
              //   BoxShadow(
              //     color: Colors.black12.withValues(alpha: 0.1),
              //     blurRadius: 10,
              //     spreadRadius: 0,
              //     offset: const Offset(0, -2),
              //   ),
              // ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              // crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    BottomNavColumn(
                      icon:
                          currentIndex == 0 ? Iconsax.home_25 : Iconsax.home_2,
                      labelText: 'Home',
                      isSelected: currentIndex == 0,
                      onPressed: () {
                        currentIndex = 0;
                        setState(() {});
                      },
                    ),
                    const XBox(20),
                    BottomNavColumn(
                      icon: currentIndex == 1
                          ? Iconsax.chart_square5
                          : Iconsax.chart_square,
                      labelText: 'My Contributions',
                      isSelected: currentIndex == 1,
                      onPressed: () {
                        currentIndex = 1;
                        setState(() {});
                      },
                    ),
                  ],
                ),
                const XBox(20),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    BottomNavColumn(
                      icon: currentIndex == 2
                          ? Iconsax.notification5
                          : Iconsax.notification,
                      isSelected: currentIndex == 2,
                      labelText: 'Notifications',
                      onPressed: () {
                        currentIndex = 2;
                        setState(() {});
                      },
                    ),
                    const XBox(20),
                    BottomNavColumn(
                      icon:
                          currentIndex == 3 ? AppSvgs.userActive : Iconsax.user,
                      labelText: 'Profile',
                      isSelected: currentIndex == 3,
                      onPressed: () {
                        currentIndex = 3;
                        setState(() {});
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
