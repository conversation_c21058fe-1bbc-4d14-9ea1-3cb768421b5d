import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:flutter/services.dart';

class AddBankAccountScreen extends ConsumerStatefulWidget {
  const AddBankAccountScreen({super.key});

  @override
  ConsumerState<AddBankAccountScreen> createState() => _DebitCardScreenState();
}

class _DebitCardScreenState extends ConsumerState<AddBankAccountScreen> {
  final bankC = TextEditingController();
  final accountNumberC = TextEditingController();
  final accountHolderC = TextEditingController();

  final bankF = FocusNode();
  final accountNumberF = FocusNode();
  final accountHolderF = FocusNode();

  String? selectedBankCode;
  String? selectedBankName;

  @override
  void initState() {
    super.initState();
    // Initialize the bank view model
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bankVmodel).getAllBanks();
    });
  }

  bool get isFormValid {
    return selectedBankCode != null &&
        bankC.text.isNotEmpty &&
        accountNumberC.text.isNotEmpty &&
        accountHolderC.text.isNotEmpty;
  }

  Future<void> addBankAccount() async {
    final response = await ref.read(bankVmodel).addBankAccount(
          bankCode: selectedBankCode ?? "",
          bankName: selectedBankName ?? "",
          accountNumber: accountNumberC.text,
          accountName: accountHolderC.text,
        );

    handleApiResponse(
      response: response!,
      onCompleted: () {
        Navigator.pop(context);
        ref.read(bankVmodel).getMyBankAccounts();
      },
    );
  }

  @override
  void dispose() {
    bankC.dispose();
    accountNumberC.dispose();
    accountHolderC.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bankRef = ref.watch(bankVmodel);
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Add Bank Account',
            subtitle: "Enter your bank account to receive credit",
            // titleSize: 24,
          ),
          const YBox(54),

          CustomTextField(
            controller: bankC,
            focusNode: bankF,
            isReadOnly: true,
            labelText: 'Bank',
            showLabelHeader: true,
            suffixIcon: Icon(
              Iconsax.arrow_down_1,
              size: Sizer.radius(24),
            ),
            onTap: () async {
              final result = await ModalWrapper.bottomSheet(
                context: context,
                widget: BankInfoModal(selectedBank: selectedBankName),
              );
              if (result is Map<String, dynamic> && context.mounted) {
                selectedBankName = result['name'];
                selectedBankCode = result['code'];
                bankC.text = selectedBankName ?? '';

                if (accountNumberC.text.isNotEmpty) {
                  final res = await ref.read(bankVmodel).verifyBankAccount(
                        accountNumber: accountNumberC.text,
                        bankCode: selectedBankCode!,
                      );

                  handleApiResponse(
                    response: res,
                    showSuccessToast: false,
                    onCompleted: () {
                      accountHolderC.text = res.data['account_name'];
                      setState(() {});
                    },
                    onError: () {
                      accountHolderC.clear();
                      setState(() {});
                    },
                  );
                }
                setState(() {});
              } else if (context.mounted) {
                return FlushBarToast.fLSnackBar(
                  snackBarType: SnackBarType.warning,
                  message: "Could not load banks. Please try again.",
                );
              }
            },
          ),

          const YBox(24),
          CustomTextField(
            controller: accountNumberC,
            focusNode: accountNumberF,
            labelText: 'Account Number',
            showLabelHeader: true,
            keyboardType: KeyboardType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(10),
            ],
            onChanged: (v) async {
              if (v.length >= 10) {
                final res = await ref.read(bankVmodel).verifyBankAccount(
                      accountNumber: accountNumberC.text,
                      bankCode: selectedBankCode!,
                    );

                handleApiResponse(
                  response: res,
                  showSuccessToast: false,
                  onCompleted: () {
                    accountHolderC.text = res.data['account_name'];
                    setState(() {});
                  },
                );
              } else {
                accountHolderC.clear();
                setState(() {});
              }
            },
            suffixIcon: bankRef.busy(validateState)
                ? const CupertinoActivityIndicator()
                : null,
          ),
          const YBox(24),
          CustomTextField(
            controller: accountHolderC,
            focusNode: accountHolderF,
            labelText: 'Account Holder',
            showLabelHeader: true,
            isReadOnly: true,
          ),

          // const YBox(24),
          // DebitScanWidget(
          //   text: 'Scan your card',
          //   icon: AppSvgs.scanDash,
          //   onTap: () {},
          // ),
          // const YBox(12),
          // DebitScanWidget(
          //   text: 'Save your card information',
          //   icon: AppSvgs.tickSquare,
          //   color: AppColors.neutral300,
          //   onTap: () {},
          // ),
          const YBox(50),
          CustomBtn.solid(
            text: 'Add Bank Account',
            textStyle: AppTypography.text16.copyWith(
              color: AppColors.white,
            ),
            isLoading: ref.watch(bankVmodel).busy(addState),
            online: isFormValid,
            onTap: addBankAccount,
          ),
          // const YBox(16),
          // SkipForNow(
          //   onTap: () {
          //     Navigator.pop(context);
          //   },
          // ),
          const YBox(100),
        ],
      ),
    );
  }
}
