import 'package:equalcash/core/core.dart';

class ModalCustomTile extends StatelessWidget {
  const ModalCustomTile({
    super.key,
    this.isSelected = false,
    this.name,
    this.onTap,
  });

  final bool isSelected;
  final String? name;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(10),
          horizontal: Sizer.width(10),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Sizer.radius(4)),
          border: Border.all(
            color: AppColors.secondary200,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Iconsax.bank,
              size: Sizer.radius(16),
              color: AppColors.neutral300,
            ),
            const XBox(10),
            Expanded(
              child: Text(
                name ?? '',
                style: AppTypography.text12.copyWith(
                  color: AppColors.neutral300,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                size: Sizer.radius(16),
                color: AppColors.primaryPurple,
              ),
          ],
        ),
      ),
    );
  }
}
