import 'package:equalcash/core/core.dart';

class SelectCurrencyModal extends StatelessWidget {
  const SelectCurrencyModal({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(24),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(Sizer.radius(24)),
        ),
        color: AppColors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Ali<PERSON>(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: Icon(
                Icons.close,
                size: Sizer.radius(28),
              ),
            ),
          ),
          const YBox(40),
          Text(
            "Select Currency",
            style: AppTypography.text20.copyWith(
              color: AppColors.neutral400,
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(6),
          Text(
            "Naira is the only available currency for now",
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              color: AppColors.neutral200,
              height: 1.5,
            ),
          ),
          const YBox(36),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "AVAILABLE CURRENCY",
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.neutral200,
                height: 1.5,
              ),
            ),
          ),
          const YBox(4),
          CurrencyCard(
            title: "Nigerian Naira",
            subtitle: "(₦)NGN",
            onTap: () {},
          ),
          const YBox(24),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "COMING SOON",
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.neutral200,
                height: 1.5,
              ),
            ),
          ),
          const YBox(4),
          CurrencyCard(
            title: "Nigerian Naira",
            subtitle: "(₦)NGN",
            onTap: () {},
          ),
          const YBox(40),
        ],
      ),
    );
  }
}

class CurrencyCard extends StatelessWidget {
  const CurrencyCard({
    super.key,
    required this.title,
    required this.subtitle,
    this.bgColor,
    this.onTap,
  });

  final String title;
  final String subtitle;
  final Color? bgColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
          vertical: Sizer.height(8),
        ),
        decoration: BoxDecoration(
          color: bgColor ?? AppColors.transparent,
          border: Border.all(
            color: AppColors.accent100,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Row(
          children: [
            SvgPicture.asset(AppSvgs.ngn),
            const XBox(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Nigerian Naira",
                    style: AppTypography.text13.copyWith(
                      color: AppColors.neutral100.withValues(alpha: 0.8),
                    ),
                  ),
                  const YBox(4),
                  Text(
                    "(₦)NGN",
                    style: AppTypography.text10.copyWith(
                      color: AppColors.neutral100.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Iconsax.arrow_down_1,
              size: Sizer.radius(20),
            ),
          ],
        ),
      ),
    );
  }
}
