import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class BankInfoModal extends ConsumerStatefulWidget {
  const BankInfoModal({
    super.key,
    this.isSelected = false,
    this.selectedBank,
  });

  final bool isSelected;
  final String? selectedBank;

  @override
  ConsumerState<BankInfoModal> createState() => _BankInfoModalState();
}

class _BankInfoModalState extends ConsumerState<BankInfoModal> {
  final searchC = TextEditingController();
  final searchF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bankVmodel).searchBanks("");
    });
  }

  @override
  void dispose() {
    searchC.dispose();
    searchF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Sizer.screenHeight * 0.8,
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(
            Sizer.radius(24),
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Select Bank',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.neutral300,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              InkWell(
                onTap: () => Navigator.pop(context),
                child: Icon(
                  Icons.close,
                  size: Sizer.radius(28),
                ),
              ),
            ],
          ),
          const YBox(10),
          CustomTextField(
            controller: searchC,
            focusNode: searchF,
            hintText: 'Search for bank',
            prefixIcon: Padding(
              padding: EdgeInsets.all(Sizer.radius(10)),
              child: SvgPicture.asset(AppSvgs.search),
            ),
            onChanged: (value) {
              ref.read(bankVmodel).searchBanks(value);
            },
            suffixIcon: searchC.text.isNotEmpty
                ? GestureDetector(
                    onTap: () {
                      searchC.clear();
                      ref.read(bankVmodel).searchBanks('');
                    },
                    child: const Icon(
                      Icons.close,
                      color: AppColors.neutral200,
                    ),
                  )
                : const Icon(
                    Iconsax.setting_4,
                    color: AppColors.neutral200,
                  ),
          ),
          Expanded(
            child: LoadableContentBuilder(
                isBusy: ref.watch(bankVmodel).isBusy,
                isError: ref.watch(bankVmodel).hasError,
                items: ref.watch(bankVmodel).filteredBanks,
                loadingBuilder: (ctx) {
                  return const SizerLoader(height: 300);
                },
                emptyBuilder: (ctx) {
                  return const EmptyListState(
                    text: 'No banks found',
                  );
                },
                errorBuilder: (ctx) {
                  return EmptyListState(
                    text: 'Failed to load banks',
                    onRetry: () {
                      ref.read(bankVmodel).getAllBanks();
                    },
                  );
                },
                contentBuilder: (context) {
                  final filteredBanks = ref.watch(bankVmodel).filteredBanks;
                  return ListView.separated(
                    shrinkWrap: true,
                    padding: EdgeInsets.only(
                      top: Sizer.height(16),
                      bottom: Sizer.height(50),
                    ),
                    itemCount: filteredBanks.length,
                    separatorBuilder: (_, __) => const YBox(10),
                    itemBuilder: (ctx, i) {
                      final bank = filteredBanks[i];
                      return ModalCustomTile(
                        name: bank.name,
                        isSelected: widget.selectedBank == bank.name,
                        onTap: () {
                          // Return both bank name and code
                          Navigator.pop(context, {
                            'name': bank.name,
                            'code': bank.code,
                          });
                        },
                      );
                    },
                  );
                }),
          ),
        ],
      ),
    );
  }
}
