import 'dart:async';

import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class CountryModal extends ConsumerStatefulWidget {
  const CountryModal({
    super.key,
    this.isSelected = false,
    this.selectedCountry,
  });

  final bool isSelected;
  final String? selectedCountry;

  @override
  ConsumerState<CountryModal> createState() => _CountryModalState();
}

class _CountryModalState extends ConsumerState<CountryModal> {
  final searchC = TextEditingController();
  final searchF = FocusNode();
  List<VerificationCountriesModel> filteredCountries = [];
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();

    // Add listener to search controller for real-time updates
    searchC.addListener(() {
      setState(() {}); // Update UI when text changes (for suffix icon)
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(kycVmodel).getVerificationCountries().then((_) {
        // Initialize filtered countries after loading
        if (mounted) {
          setState(() {
            filteredCountries = ref.read(kycVmodel).verificationCountries;
          });
        }
      });
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    searchC.dispose();
    searchF.dispose();
    super.dispose();
  }

  void _filterCountries(String query) {
    final countries = ref.read(kycVmodel).verificationCountries;

    if (query.isEmpty || query.trim().isEmpty) {
      filteredCountries = List.from(countries);
    } else {
      final searchQuery = query.toLowerCase().trim();
      filteredCountries = countries.where((country) {
        final name = (country.name ?? '').toLowerCase();
        final code = (country.code ?? '').toLowerCase();

        // Search in both country name and country code
        return name.contains(searchQuery) ||
            code.contains(searchQuery) ||
            name.startsWith(searchQuery) ||
            code.startsWith(searchQuery);
      }).toList();

      // Sort results: exact matches first, then starts with, then contains
      filteredCountries.sort((a, b) {
        final aName = (a.name ?? '').toLowerCase();
        final bName = (b.name ?? '').toLowerCase();
        final aCode = (a.code ?? '').toLowerCase();
        final bCode = (b.code ?? '').toLowerCase();

        // Exact matches first
        if (aName == searchQuery || aCode == searchQuery) return -1;
        if (bName == searchQuery || bCode == searchQuery) return 1;

        // Starts with matches second
        if (aName.startsWith(searchQuery) || aCode.startsWith(searchQuery))
          return -1;
        if (bName.startsWith(searchQuery) || bCode.startsWith(searchQuery))
          return 1;

        // Alphabetical order for the rest
        return aName.compareTo(bName);
      });
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    printty("Has countries : ${ref.watch(kycVmodel).hasError}");
    return Container(
      height: Sizer.screenHeight * 0.85,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(Sizer.radius(24)),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: Sizer.height(8)),
            width: Sizer.width(40),
            height: Sizer.height(4),
            decoration: BoxDecoration(
              color: AppColors.neutral200,
              borderRadius: BorderRadius.circular(Sizer.radius(2)),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(20),
              vertical: Sizer.height(16),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Country',
                    style: AppTypography.text18.copyWith(
                      color: AppColors.neutral400,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () => Navigator.pop(context),
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: Container(
                    padding: EdgeInsets.all(Sizer.radius(8)),
                    child: Icon(
                      Icons.close,
                      size: Sizer.radius(24),
                      color: AppColors.neutral300,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Search field
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
            child: CustomTextField(
              controller: searchC,
              focusNode: searchF,
              hintText: 'Search countries...',
              prefixIcon: Padding(
                padding: EdgeInsets.all(Sizer.radius(12)),
                child: SvgPicture.asset(
                  AppSvgs.search,
                  width: Sizer.radius(20),
                  height: Sizer.radius(20),
                ),
              ),
              onChanged: (value) {
                _debounceTimer?.cancel();
                _debounceTimer = Timer(const Duration(milliseconds: 300), () {
                  _filterCountries(value);
                });
              },
              suffixIcon: searchC.text.isNotEmpty
                  ? InkWell(
                      onTap: () {
                        searchC.clear();
                        _filterCountries('');
                      },
                      child: Icon(
                        Icons.close,
                        color: AppColors.neutral200,
                        size: Sizer.radius(20),
                      ),
                    )
                  : null,
            ),
          ),

          const YBox(16),

          // Countries list
          Expanded(
            child: LoadableContentBuilder(
                isBusy: ref.watch(kycVmodel).isBusy,
                isError: ref.watch(kycVmodel).hasError,
                items: ref.watch(kycVmodel).verificationCountries,
                loadingBuilder: (ctx) {
                  return const SizerLoader(height: 300);
                },
                emptyBuilder: (ctx) {
                  return EmptyListState(
                    text: searchC.text.isNotEmpty
                        ? 'No countries match your search'
                        : 'No countries found',
                  );
                },
                errorBuilder: (ctx) {
                  return EmptyListState(
                    text: 'Failed to load countries',
                    onRetry: () {
                      ref.read(kycVmodel).getVerificationCountries();
                    },
                  );
                },
                contentBuilder: (context) {
                  final countries = searchC.text.isEmpty
                      ? ref.watch(kycVmodel).verificationCountries
                      : filteredCountries;

                  if (searchC.text.isNotEmpty && filteredCountries.isEmpty) {
                    return const EmptyListState(
                      text: 'No countries match your search',
                    );
                  }

                  return ListView.separated(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(20),
                      vertical: Sizer.height(8),
                    ),
                    itemCount: countries.length,
                    separatorBuilder: (_, __) => const YBox(8),
                    itemBuilder: (ctx, i) {
                      final country = countries[i];
                      return EnhancedCountryTile(
                        country: country,
                        isSelected: widget.selectedCountry == country.name,
                        onTap: () {
                          Navigator.pop(context, {
                            'name': country.name,
                            'code': country.code,
                          });
                        },
                      );
                    },
                  );
                }),
          ),
        ],
      ),
    );
  }
}

class EnhancedCountryTile extends StatelessWidget {
  const EnhancedCountryTile({
    super.key,
    required this.country,
    this.isSelected = false,
    this.onTap,
  });

  final VerificationCountriesModel country;
  final bool isSelected;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(10),
          horizontal: Sizer.width(16),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Sizer.radius(12)),
          border: Border.all(
            color:
                isSelected ? AppColors.primaryPurple : AppColors.secondary200,
            width: isSelected ? 2 : 1,
          ),
          color: isSelected ? AppColors.secondary50 : AppColors.white,
        ),
        child: Row(
          children: [
            // Country flag placeholder (using globe icon)
            Container(
              width: Sizer.radius(30),
              height: Sizer.radius(30),
              decoration: BoxDecoration(
                color: AppColors.secondary100,
                borderRadius: BorderRadius.circular(Sizer.radius(16)),
              ),
              child: Icon(
                Icons.public,
                size: Sizer.radius(18),
                color: AppColors.primaryPurple,
              ),
            ),

            const XBox(12),

            // Country info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    country.name ?? '',
                    style: AppTypography.text12.copyWith(
                      color: AppColors.neutral400,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (country.code != null) ...[
                    const YBox(2),
                    Text(
                      country.code!.toUpperCase(),
                      style: AppTypography.text10.copyWith(
                        color: AppColors.neutral300,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Selection indicator
            if (isSelected)
              Container(
                width: Sizer.radius(16),
                height: Sizer.radius(16),
                decoration: BoxDecoration(
                  color: AppColors.primaryPurple,
                  borderRadius: BorderRadius.circular(Sizer.radius(12)),
                ),
                child: Icon(
                  Icons.check,
                  size: Sizer.radius(16),
                  color: AppColors.white,
                ),
              )
            else
              Container(
                width: Sizer.radius(16),
                height: Sizer.radius(16),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.secondary200,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(Sizer.radius(12)),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
