import 'package:equalcash/core/core.dart';

void handleApiResponse({
  required ApiResponse response,
  int? duration,
  String? successMsg,
  bool showErrorToast = true,
  bool showSuccessToast = true,
  void Function()? onCompleted,
  void Function()? onError,
}) {
  if (response.success) {
    onCompleted?.call();

    if (showSuccessToast) {
      showSuccessToastMessage(
          successMsg ?? response.message ?? 'Operation successful');
    }
  } else {
    onError?.call();
    if (showErrorToast) {
      showWarningToast(response.message ?? 'Something went wrong');
    }
  }
}

/// Shows an error toast message
void showWarningToast(String msg) {
  FlushBarToast.fLSnackBar(message: msg);
}

/// Shows a success toast message
void showSuccessToastMessage(String msg) {
  FlushBarToast.fLSnackBar(
    message: msg,
    snackBarType: SnackBarType.success,
  );
}
