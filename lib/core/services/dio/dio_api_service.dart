import 'dart:async';

import 'package:dio/dio.dart';
import 'package:equalcash/core/core.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

class DioApiService {
  int timeOutDurationInSeconds = 30;
  int connectionTimeout = 6000;
  AppInterceptors appInterceptors;
  late Dio dio;

  var options = BaseOptions(
    baseUrl: AppConfig.baseUrl,
    connectTimeout: const Duration(seconds: 6000),
    receiveTimeout: const Duration(seconds: 3000),
  );

  PrettyDioLogger logger = PrettyDioLogger(
    requestHeader: true,
    requestBody: true,
    responseBody: true,
    responseHeader: false,
    error: true,
    compact: true,
    maxWidth: 10090,
  );

  DioApiService({required this.appInterceptors}) {
    dio = Dio(options);
    dio.interceptors.add(appInterceptors);
    dio.interceptors.add(logger);
    Map<String, dynamic> headers = {'Accept': 'application/json'};
    dio.options.headers = headers;
  }

  Future<ApiResponse> post({
    var body,
    required String url,
    bool isFormData = true,
  }) async {
    try {
      options.headers = await getDeviceHeaders();

      final dio = Dio(options);
      dio.interceptors.add(logger);
      Response response = await dio
          .post(url, data: isFormData ? FormData.fromMap(body) : body)
          .timeout(Duration(seconds: timeOutDurationInSeconds));
      return DioResponseHandler.parseResponse(response);
    } on DioException catch (e) {
      printty("error");
      return DioResponseHandler.dioErrorHandler(e);
    }
  }

  Future<ApiResponse> get({
    dynamic body,
    required String url,
  }) async {
    try {
      final dio = Dio(options);
      dio.interceptors.add(logger);
      Response response = await dio
          .get(url)
          .timeout(Duration(seconds: timeOutDurationInSeconds));
      return DioResponseHandler.parseResponse(response);
    } on DioException catch (e) {
      printty('Dio get error: $e');
      return DioResponseHandler.dioErrorHandler(e);
    }
  }

  Future<ApiResponse> put({
    required Map<String, dynamic> body,
    required String url,
    bool isFormData = true,
  }) async {
    try {
      // await HeaderService().getDeviceInfo();
      var options = BaseOptions();
      options.headers = await getDeviceHeaders();
      Response response = await Dio(options)
          .put(url, data: isFormData ? FormData.fromMap(body) : body)
          .timeout(Duration(seconds: timeOutDurationInSeconds));
      return DioResponseHandler.parseResponse(response);
    } on DioException catch (e) {
      return DioResponseHandler.dioErrorHandler(e);
    }
  }

  Future<ApiResponse> postWithAuth({
    var body,
    required String url,
    bool canRetry = true,
    bool isFormData = true,
  }) async {
    try {
      // await HeaderService().getDeviceInfo();
      dio.options.headers.addAll(await getDeviceHeaders());

      dynamic data = body;
      if (body != null) {
        data = isFormData ? FormData.fromMap(body) : body;
      }

      Response response = await dio
          .post(url, data: data)
          .timeout(Duration(seconds: timeOutDurationInSeconds));
      return DioResponseHandler.parseResponse(response);
    } on DioException catch (e) {
      return DioResponseHandler.dioErrorHandler(e);
    } catch (e) {
      return ApiResponse(success: false);
    }
  }

  Future<ApiResponse> deleteWithAuth({
    var body,
    required String url,
    bool canRetry = true,
    String? contentType,
  }) async {
    try {
      // await HeaderService().getDeviceInfo();
      dio.options.headers.addAll(await getDeviceHeaders());

      if (contentType != null) {
        dio.options.contentType = contentType;
      }

      dynamic data = body;
      if (body != null) {
        data = FormData.fromMap(body);
      }

      Response response = await dio
          .delete(url, data: contentType == null ? data : body)
          .timeout(Duration(seconds: timeOutDurationInSeconds));
      return DioResponseHandler.parseResponse(response);
    } on DioException catch (e) {
      return DioResponseHandler.dioErrorHandler(e);
    } catch (e) {
      return ApiResponse(success: false);
    }
  }

  Future<ApiResponse> getWithAuth({
    var body,
    required String url,
    bool canRetry = true,
    isFormData = false,
  }) async {
    try {
      // printty("getDeviceHeaders : ${await getDeviceHeaders()}");
      // dio.options.headers = await getDeviceHeaders();
      dio.options.headers.addAll(await getDeviceHeaders());

      Response response = await dio
          .get(url)
          .timeout(Duration(seconds: timeOutDurationInSeconds));
      return DioResponseHandler.parseResponse(response);
    } on DioException catch (e, s) {
      printty('Dio get error: $e, StackTrace: $s');
      return DioResponseHandler.dioErrorHandler(e);
    }
  }

  Future<ApiResponse> printtyout() async {
    await StorageService.logout();
    return ApiResponse(
        code: 401, success: false, message: "Unauthorized. Access denied!!!");
  }
}

Future<Map<String, String>> getDeviceHeaders() async {
  return {
    "Device-Id": await StorageService.getStringItem(StorageKey.deviceId) ?? "",
    "Device-Type":
        await StorageService.getStringItem(StorageKey.deviceType) ?? "",
    "Device-Name":
        await StorageService.getStringItem(StorageKey.deviceName) ?? "",
    "Notification-Token":
        await StorageService.getStringItem(StorageKey.deviceToken) ?? "",
    "App-Version":
        await StorageService.getStringItem(StorageKey.appVersion) ?? "",
  };
}

DioApiService apiService = DioApiService(appInterceptors: AppInterceptors());
