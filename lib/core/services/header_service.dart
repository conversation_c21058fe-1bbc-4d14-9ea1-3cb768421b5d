import 'package:device_info_plus/device_info_plus.dart';
import 'package:equalcash/core/core.dart';
import 'package:flutter/services.dart';

class HeaderService {
  String? deviceId;
  String? deviceType;
  String? deviceName;
  String? notification;

  getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    
    if (Platform.isIOS) {
      printty("ios_______");
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      StorageService.storeStringItem(
          StorageKey.deviceId, iosInfo.identifierForVendor ?? "");
      StorageService.storeStringItem(StorageKey.deviceType, "IOS");
      StorageService.storeStringItem(
          StorageKey.deviceName, iosInfo.name);
      StorageService.storeStringItem(
          StorageKey.deviceModel, iosInfo.model);
    }
    if (Platform.isAndroid) {
      printty("andriod_______");
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      StorageService.storeStringItem(StorageKey.deviceId, androidInfo.id);
      StorageService.storeStringItem(StorageKey.deviceType, "Android");
      StorageService.storeStringItem(StorageKey.deviceName, androidInfo.model);
      StorageService.storeStringItem(StorageKey.deviceModel, androidInfo.model);
    }
  }

  deviceInformation() async {
    printty("=======+++++++++");
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        printty("device information: ======>");
        printty(iosInfo.systemVersion.toString());
        printty(iosInfo.identifierForVendor.toString());
        printty(iosInfo.model.toString());
        printty(iosInfo.name.toString());
        printty(iosInfo.systemName.toString());
        printty("device information: iOS");
      } else if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        printty("device information: ======>");
        printty(androidInfo.version.release.toString());
        printty(androidInfo.id.toString());
        printty(androidInfo.model.toString());
        printty(androidInfo.manufacturer.toString());
        printty(androidInfo.version.sdkInt.toString());
        printty(androidInfo.device.toString());
        printty(androidInfo.product.toString());
        printty(androidInfo.hardware.toString());
        printty("device information: Android");
      }
    } on PlatformException {
      printty("&&&&&&&&&&&&&");
      'Failed to get platform version.';
    } catch (e) {
      printty("+++++++++++++");
      printty(e.toString());
    }
  }
}

class FCMTokenService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  Future<String?> getToken() async {
    try {
      // On iOS, ensure APNS token is available first
      if (Platform.isIOS) {
        await _ensureAPNSTokenAvailable();
      }

      // Always get a fresh token from Firebase
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        // Store the token
        await StorageService.storeStringItem(StorageKey.deviceToken, token);
        printty("FCM token retrieved and stored: $token");
      } else {
        printty("Warning: Firebase returned null token");
      }
      return token;
    } catch (e) {
      printty("Error getting FCM token: $e");
      return null;
    }
  }

  Future<void> _ensureAPNSTokenAvailable() async {
    try {
      // Wait for APNS token to be available
      String? apnsToken = await _firebaseMessaging.getAPNSToken();

      if (apnsToken == null) {
        printty("APNS token not available, waiting...");

        // Wait up to 10 seconds for APNS token
        int attempts = 0;
        while (apnsToken == null && attempts < 20) {
          await Future.delayed(const Duration(milliseconds: 500));
          apnsToken = await _firebaseMessaging.getAPNSToken();
          attempts++;
        }

        if (apnsToken != null) {
          printty(
              "APNS token became available: ${apnsToken.substring(0, 20)}...");
        } else {
          printty("Warning: APNS token still not available after waiting");
        }
      } else {
        printty(
            "APNS token already available: ${apnsToken.substring(0, 20)}...");
      }
    } catch (e) {
      printty("Error checking APNS token: $e");
    }
  }

  void setupTokenRefreshListener() {
    // Listen for token refreshes
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
      printty("FCM token refreshed: $newToken");
      // Store the new token
      await StorageService.storeStringItem(StorageKey.deviceToken, newToken);

      // Here you could also send the new token to your backend
      // This ensures your backend always has the latest token
      _updateTokenOnBackend(newToken);
    }).onError((err) {
      printty("FCM token refresh error: $err");
    });
  }

  Future<void> _updateTokenOnBackend(String token) async {
    // This method would call your backend API to update the token
    // Implement according to your backend API requirements
    try {
      // Use the existing apiService instance
      await apiService.postWithAuth(
        url: "/auth/update-device-token",
        body: {"device_token": token},
      );
      printty("FCM token updated on backend");
    } catch (e) {
      printty("Error updating FCM token on backend: $e");
    }
  }
}
