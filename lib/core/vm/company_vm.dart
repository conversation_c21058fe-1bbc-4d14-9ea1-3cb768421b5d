import 'package:equalcash/lib.dart';

const String createdCompState = "createdCompState";
const String joinedCompState = "joinedCompState";
const String acceptRejectCompState = "acceptRejectCompState";

class CompanyVm extends BaseVm {
  List<CompanyModel> _allCompanies = [];
  List<CompanyModel> get allCompanies => _allCompanies;
  List<CompanyModel> _companiesCreatedByMe = [];
  List<CompanyModel> get companiesCreatedByMe => _companiesCreatedByMe;
  List<CompanyModel> _companiesJoined = [];
  List<CompanyModel> get companiesJoined => _companiesJoined;
  CompanyModel? _companyDetails;
  CompanyModel? get companyDetails => _companyDetails;

  Future<ApiResponse> getAllCompanies() async {
    return await performApiCall(
      url: "/v1/companies",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _allCompanies =
            companyModelFromJson(json.encode(data["datatable"]["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getCreatedCompanies() async {
    return await performApiCall(
      url: "/v1/companies/created",
      method: apiService.getWithAuth,
      busyObjectName: createdCompState,
      onSuccess: (data) {
        _companiesCreatedByMe =
            companyModelFromJson(json.encode(data["datatable"]["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getJoinedCompanies() async {
    return await performApiCall(
      url: "/v1/companies/joined",
      method: apiService.getWithAuth,
      busyObjectName: joinedCompState,
      onSuccess: (data) {
        _companiesJoined =
            companyModelFromJson(json.encode(data["datatable"]["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getCompanyDetails({required String companyId}) async {
    return await performApiCall(
      url: "/v1/companies/$companyId/view",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _companyDetails = CompanyModel.fromJson(data["data"]);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getDynamicTypePath(String url) async {
    return await performApiCall(
      url: "/v1/$url",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> createCompany({
    required int currencyId,
    required String name,
    required String cashBucket,
    required int duration,
    required String type,
    required int numberOfStakeholders,
    required String commencementDate,
    required String collectionOrderExchangeFee,
    required List<String> members,
  }) async {
    Map<String, dynamic> body = {
      "currency_id": currencyId,
      "name": name,
      "cash_bucket": cashBucket,
      "duration": duration,
      "type": type,
      "number_of_stakeholders": numberOfStakeholders,
      "commencement_date": commencementDate,
      "members": members,
      "collection_order_exchange_fee": collectionOrderExchangeFee,
    };
    return await performApiCall(
      url: "/v1/companies",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateCompany({
    required String name,
    required String cashBucket,
    required String commencementDate,
    required String companyId,
  }) async {
    Map<String, dynamic> body = {
      "name": name,
      "cash_bucket": cashBucket,
      "commencement_date": commencementDate,
    };
    return await performApiCall(
      url: "/v1/companies/$companyId/update",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  //Accept or reject company invitation
  Future<ApiResponse> acceptOrRejectCompanyInvitation({
    required int companyId,
    bool acceptInvitation = true,
  }) async {
    return await performApiCall(
      url: "/v1/companies/$companyId/${acceptInvitation ? 'accept' : 'reject'}",
      method: apiService.postWithAuth,
      busyObjectName: acceptRejectCompState,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> conditionCompanyInvitation({
    required int companyId,
    required String note,
  }) async {
    return await performApiCall(
      url: "/v1/companies/$companyId/conditional",
      method: apiService.postWithAuth,
      busyObjectName: acceptRejectCompState,
      body: {"note": note},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  // Join company
  Future<ApiResponse> joinCompany({
    required int companyId,
  }) async {
    return await performApiCall(
      url: "/v1/companies/$companyId/join",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  // Remove company member
  Future<ApiResponse> removeCompanyMember({
    required String companyId,
    required String memberId,
  }) async {
    return await performApiCall(
      url: "/v1/companies/:$companyId/remove-member/:$memberId",
      method: apiService.postWithAuth,
      body: {},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  // Get collection Order
  Future<ApiResponse> getCollectionOrder({
    required String companyId,
  }) async {
    return await performApiCall(
      url: "/v1/companies/:$companyId/collections-order",
      method: apiService.getWithAuth,
      body: {},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  // Agree to collection order
  Future<ApiResponse> agreeToCollectionOrder({
    required String companyId,
  }) async {
    return await performApiCall(
      url: "/v1/companies/$companyId/collections-order/agree",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  // Trade collections Order
  Future<ApiResponse> tradeCollectionOrder({
    required String companyId,
    required int targetMemberId,
    required String comment,
  }) async {
    return await performApiCall(
      url: "/v1/companies/$companyId/collections-order/trade",
      method: apiService.postWithAuth,
      body: {"target_member_id": targetMemberId, "comment": comment},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}

final companyVmodel = ChangeNotifierProvider<CompanyVm>((ref) {
  return CompanyVm();
});
