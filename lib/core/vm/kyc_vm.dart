import 'package:equalcash/core/core.dart';

class KycVm extends BaseVm {
  List<VerificationCountriesModel> _verificationCountries = [];
  List<VerificationCountriesModel> get verificationCountries =>
      _verificationCountries;
  Future<ApiResponse> getVerificationCountries() async {
    return await performApiCall(
      url: "/v1/verifications/countries",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        final res =
            verificationCountriesModelFromJson(json.encode(data["data"]));
        _verificationCountries = res;
        return apiResponse;
      },
    );
  }

  SdkToken? _sdkToken;
  SdkToken? get sdkToken => _sdkToken;

  Future<ApiResponse> generateSDKToken() async {
    return await performApiCall(
      url: "/v1/verifications/smile/generate-sdk-token",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        _sdkToken = SdkToken.fromJson(data["data"]);
        return apiResponse;
      },
    );
  }
}

final kycVmodel = ChangeNotifierProvider<KycVm>((ref) => KycVm());
