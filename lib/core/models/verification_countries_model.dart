import 'dart:convert';

List<VerificationCountriesModel> verificationCountriesModelFromJson(
        String str) =>
    List<VerificationCountriesModel>.from(
        json.decode(str).map((x) => VerificationCountriesModel.fromJson(x)));

class VerificationCountriesModel {
  final int? id;
  final String? name;
  final String? code;
  final List<SupportedDocument>? supportedDocuments;

  VerificationCountriesModel({
    this.id,
    this.name,
    this.code,
    this.supportedDocuments,
  });

  factory VerificationCountriesModel.fromJson(Map<String, dynamic> json) =>
      VerificationCountriesModel(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        supportedDocuments: json["supported_documents"] == null
            ? []
            : List<SupportedDocument>.from(json["supported_documents"]!
                .map((x) => SupportedDocument.fromJson(x))),
      );
}

class SupportedDocument {
  final String? idName;
  final String? idType;

  SupportedDocument({
    this.idName,
    this.idType,
  });

  factory SupportedDocument.fromJson(Map<String, dynamic> json) =>
      SupportedDocument(
        idName: json["id_name"],
        idType: json["id_type"],
      );
}

class SdkToken {
  final String? jobId;
  final int? userReference;
  final String? callbackUrl;

  SdkToken({
    this.jobId,
    this.userReference,
    this.callbackUrl,
  });

  factory SdkToken.fromJson(Map<String, dynamic> json) => SdkToken(
        jobId: json["job_id"],
        userReference: json["user_reference"],
        callbackUrl: json["callback_url"],
      );
}
