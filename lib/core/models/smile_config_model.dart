import 'package:equalcash/lib.dart';
import 'package:flutter/services.dart';

class SmileConfigModel {
  final String authToken;
  final String partnerId;
  final String prodLambdaUrl;
  final String prodUrl;
  final String testLambdaUrl;
  final String testUrl;
  final String version;

  SmileConfigModel({
    required this.authToken,
    required this.partnerId,
    required this.prodLambdaUrl,
    required this.prodUrl,
    required this.testLambdaUrl,
    required this.testUrl,
    required this.version,
  });

  factory SmileConfigModel.fromJson(Map<String, dynamic> json) {
    return SmileConfigModel(
      authToken: json['auth_token'] ?? '',
      partnerId: json['partner_id'] ?? '',
      prodLambdaUrl: json['prod_lambda_url'] ?? '',
      prodUrl: json['prod_url'] ?? '',
      testLambdaUrl: json['test_lambda_url'] ?? '',
      testUrl: json['test_url'] ?? '',
      version: json['version'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'auth_token': authToken,
      'partner_id': partnerId,
      'prod_lambda_url': prodLambdaUrl,
      'prod_url': prodUrl,
      'test_lambda_url': testLambdaUrl,
      'test_url': testUrl,
      'version': version,
    };
  }

  static Future<SmileConfigModel> loadFromAssets() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/smile_config.json');
      printty("jsonString: $jsonString");
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      return SmileConfigModel.fromJson(jsonMap);
    } catch (e) {
      throw Exception('Failed to load SmileID configuration: $e');
    }
  }
}
