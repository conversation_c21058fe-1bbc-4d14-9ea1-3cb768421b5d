{"originHash": "6ac1c974bd1eb2cb3d6261f24935e4bc01aa639c55855dcf3b57b7840cdcc5d1", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "fdc352fabaf5916e7faa1f96ad02b1957e93e5a5", "version": "11.15.0"}}, {"identity": "flutterfire", "kind": "remoteSourceControl", "location": "https://github.com/firebase/flutterfire", "state": {"revision": "a80a123386fd4904cad6938673020a7bcf31b2f2", "version": "3.13.0-firebase-core-swift"}}, {"identity": "google-ads-on-device-conversion-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googleads/google-ads-on-device-conversion-ios-sdk", "state": {"revision": "973bd67929bfbe465f63c05cd5a4b113765fbc7f", "version": "2.2.1"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "45ce435e9406d3c674dd249a042b932bee006f60", "version": "11.15.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "c756a29784521063b6a1202907e2cc47f41b667c", "version": "4.5.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "102a647b573f60f73afdce5613a51d71349fe507", "version": "1.30.0"}}], "version": 3}